Rails.application.routes.draw do
  devise_for :users

  # API routes
  namespace :api do
    # Authentication endpoints
    post "login", to: "auth#login"
    post "register", to: "auth#register"
    delete "logout", to: "auth#logout"
    get "me", to: "auth#me"

    # API v1 namespace for versioned endpoints
    namespace :v1 do
      # TODO: Add API endpoints here
      # resources :pipelines
      # resources :connections
      # resources :executions
    end
  end

  # Web application routes for subdomains (excluding www)
  constraints(subdomain: /^(?!www)[a-z0-9]+$/) do
    root "dashboard#index", as: :subdomain_root

    # Dashboard AJAX endpoints
    get "dashboard/metrics", to: "dashboard#metrics"
    get "dashboard/pipeline_metrics", to: "dashboard#pipeline_metrics"
    get "dashboard/connector_metrics", to: "dashboard#connector_metrics"
    get "dashboard/usage_metrics", to: "dashboard#usage_metrics"
    get "dashboard/system_health", to: "dashboard#system_health"
    get "dashboard/recent_activity", to: "dashboard#recent_activity"

    # User Profile Management
    namespace :users do
      resource :profile, only: [:show, :update], controller: 'profile' do
        member do
          patch :change_password
          patch :notification_preferences, to: 'profile#update_notification_preferences'
          patch :preferences, to: 'profile#update_preferences'
          post :enable_two_factor, to: 'profile#enable_two_factor'
          delete :disable_two_factor, to: 'profile#disable_two_factor'
          get :export, to: 'profile#export_data'
          delete 'sessions/:session_id', to: 'profile#revoke_session', as: :revoke_session
          delete :sessions, to: 'profile#revoke_all_sessions', as: :revoke_all_sessions
        end
      end
    end

    # Onboarding flow
    resources :onboarding, only: [ :index ] do
      collection do
        get :welcome
        get :setup_profile
        get :create_first_connection
        get :create_first_pipeline
        get :invite_team
        post :complete
        patch :mark_step_complete
        post :create_connector
        post :create_pipeline
      end
    end

    # Subscription management
    resource :subscription, only: [ :show, :create, :update ] do
      member do
        patch :cancel, to: "subscriptions#cancel"
        get :confirm, to: "subscriptions#confirm"
      end
    end

    # Team management
    resources :team_members do
      collection do
        get :accept_invitation
        patch :reject_invitation
      end
    end

    # Core data pipeline resources
    resources :pipelines do
      member do
        post :execute
        patch :toggle_status
      end
    end

    # Project-based data connectors
    resources :projects do
      resources :data_connectors, path: "connectors" do
        member do
          post :test_connection
        end
      end
    end

    # Legacy route for backward compatibility (redirects to first project)
    resources :data_connectors, path: "connectors", only: [:index] do
      member do
        post :test_connection
      end
    end

    # Analytics routes
    resources :analytics, only: [ :index ] do
      collection do
        get :pipeline_performance
        get :data_quality
        get :usage_trends
      end
    end

    # Notification routes
    resources :notifications, only: [ :index, :show, :destroy ] do
      member do
        patch :mark_as_read
        patch :mark_as_unread
      end
      collection do
        get :unread
        patch :mark_all_as_read
        get :count
      end
    end

    # AI-powered insights and analytics
    namespace :ai do
      resources :pipeline_insights, only: [ :show ] do
        member do
          get :opportunities
          get :market_analysis
          get :pricing_recommendations
        end
        collection do
          get :bulk_analysis
        end
      end

      # Data product recommendations and monetization
      resources :pipelines, only: [] do
        resources :data_product_recommendations, only: [ :index, :show ], path: "product_recommendations" do
          collection do
            get :compare
            get :market_analysis
            get :revenue_projections
            get :export_recommendations
          end
          member do
            get :implementation_plan
          end
        end
      end
    end

    # AI Agent features for passive income system
    namespace :agent do
      resources :recommendations, only: [ :index, :show ] do
        member do
          patch :accept
          patch :reject
          post :implement
        end
        collection do
          post :generate
          post :refresh
          get :analytics
        end
      end

      resources :templates, only: [ :index, :show, :create ] do
        member do
          post :purchase
          get :preview
        end
        collection do
          get :recommendations
          get :marketplace_analytics
          get :my_templates
          post :create_from_pipeline
        end
      end

      resources :revenue, only: [ :index ] do
        collection do
          get :dashboard
          get :analytics
          get :export
          get :mrr_analysis
          get :subscription_analysis
        end
      end
    end
  end

  # Stripe webhooks (no subdomain constraint)
  namespace :stripe do
    resources :webhooks, only: [ :create ]
  end

  # Health check and monitoring endpoints
  get "up" => "rails/health#show", as: :rails_health_check
  get "health" => "monitoring#health", as: :health_check
  get "metrics" => "monitoring#metrics", as: :system_metrics
  
  # Admin monitoring routes
  namespace :admin do
    get "monitoring" => "monitoring#dashboard", as: :monitoring_dashboard
    get "monitoring/capacity" => "monitoring#capacity", as: :monitoring_capacity
    get "monitoring/prometheus" => "monitoring#prometheus", as: :monitoring_prometheus
  end

  # PWA files
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Default root route for main domain (no subdomain or www) - must be last
  root "marketing#index"
end
