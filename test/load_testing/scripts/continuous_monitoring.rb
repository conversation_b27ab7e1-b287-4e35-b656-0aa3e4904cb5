#!/usr/bin/env ruby
# Continuous Performance Monitoring Script for DataReflow.io
# 
# This script monitors the application performance, tracks capacity,
# and sends alerts when thresholds are exceeded. Can be run as a 
# background process or scheduled via cron.

require 'net/http'
require 'json'
require 'time'
require 'fileutils'

class ContinuousMonitor
  def initialize(base_url = 'http://localhost:3000')
    @base_url = base_url
    @monitoring_log = 'test/load_testing/logs/continuous_monitoring.log'
    @alerts_log = 'test/load_testing/logs/alerts.log'
    @metrics_history = 'test/load_testing/logs/metrics_history.json'
    
    # Create log directories
    FileUtils.mkdir_p(File.dirname(@monitoring_log))
    FileUtils.mkdir_p(File.dirname(@alerts_log))
    FileUtils.mkdir_p(File.dirname(@metrics_history))
    
    # Alert thresholds (can be configured)
    @thresholds = {
      capacity_warning: 75,      # % capacity usage
      capacity_critical: 90,     # % capacity usage
      response_time_warning: 500, # ms
      response_time_critical: 1000, # ms
      error_rate_warning: 2,     # %
      error_rate_critical: 5,    # %
      active_users_max: 15,      # concurrent users
      db_pool_warning: 80,       # % database pool usage
      memory_warning: 500        # MB memory usage
    }
    
    log("Continuous monitoring initialized for #{@base_url}")
  end
  
  def start_monitoring(interval_seconds = 30)
    log("Starting continuous monitoring with #{interval_seconds}s intervals")
    
    loop do
      begin
        collect_and_analyze_metrics
        sleep(interval_seconds)
      rescue Interrupt
        log("Monitoring stopped by user")
        break
      rescue => e
        log("Error in monitoring loop: #{e.message}")
        sleep(interval_seconds)
      end
    end
  end
  
  def run_once
    log("Running single monitoring check")
    collect_and_analyze_metrics
  end
  
  private
  
  def collect_and_analyze_metrics
    timestamp = Time.current
    
    # Collect current metrics
    health_status = fetch_health_status
    metrics = fetch_metrics
    capacity = fetch_capacity
    
    unless health_status && metrics && capacity
      log("Failed to collect metrics from #{@base_url}")
      return
    end
    
    # Analyze metrics and check thresholds
    analysis = analyze_metrics(health_status, metrics, capacity, timestamp)
    
    # Log current status
    log_status(analysis, timestamp)
    
    # Store metrics history
    store_metrics_history(analysis, timestamp)
    
    # Check for alerts
    check_alerts(analysis, timestamp)
    
    # Display current status
    display_status(analysis) if ENV['VERBOSE']
  end
  
  def fetch_health_status
    fetch_endpoint('/health')
  end
  
  def fetch_metrics
    fetch_endpoint('/metrics')
  end
  
  def fetch_capacity
    # This endpoint might require authentication in production
    # For testing, we'll skip if it returns 403/401
    result = fetch_endpoint('/admin/monitoring/capacity')
    return nil if result&.dig('error')
    result
  end
  
  def fetch_endpoint(path)
    uri = URI("#{@base_url}#{path}")
    
    begin
      response = Net::HTTP.get_response(uri)
      
      if response.code.to_i == 200
        JSON.parse(response.body)
      elsif response.code.to_i.between?(401, 403)
        # Skip authentication-required endpoints for now
        nil
      else
        log("HTTP #{response.code} for #{path}")
        nil
      end
    rescue => e
      log("Error fetching #{path}: #{e.message}")
      nil
    end
  end
  
  def analyze_metrics(health, metrics, capacity, timestamp)
    analysis = {
      timestamp: timestamp,
      status: 'healthy',
      issues: [],
      warnings: [],
      alerts: []
    }
    
    # Overall health status
    if health['status'] != 'healthy'
      analysis[:status] = health['status']
      analysis[:issues] << "System health: #{health['status']}"
    end
    
    # Database performance
    if health.dig('checks', 'database', 'status') != 'healthy'
      analysis[:issues] << "Database health issues detected"
    end
    
    db_response_time = health.dig('checks', 'database', 'response_time')&.gsub('ms', '')&.to_f
    if db_response_time && db_response_time > 100
      analysis[:warnings] << "Database response time: #{db_response_time}ms"
    end
    
    # Application performance
    if metrics
      response_time = metrics.dig('application', 'avg_response_time')
      error_rate = metrics.dig('application', 'error_rate')
      
      if response_time
        if response_time > @thresholds[:response_time_critical]
          analysis[:alerts] << {
            type: 'response_time_critical',
            message: "Response time critical: #{response_time}ms",
            value: response_time,
            threshold: @thresholds[:response_time_critical]
          }
        elsif response_time > @thresholds[:response_time_warning]
          analysis[:warnings] << "Response time elevated: #{response_time}ms"
        end
      end
      
      if error_rate
        if error_rate > @thresholds[:error_rate_critical]
          analysis[:alerts] << {
            type: 'error_rate_critical',
            message: "Error rate critical: #{error_rate}%",
            value: error_rate,
            threshold: @thresholds[:error_rate_critical]
          }
        elsif error_rate > @thresholds[:error_rate_warning]
          analysis[:warnings] << "Error rate elevated: #{error_rate}%"
        end
      end
    end
    
    # Capacity analysis
    if capacity
      capacity_used = capacity['capacity_used']&.gsub('%', '')&.to_i
      active_users = capacity['active_users']
      
      if capacity_used
        if capacity_used > @thresholds[:capacity_critical]
          analysis[:alerts] << {
            type: 'capacity_critical',
            message: "System capacity critical: #{capacity_used}%",
            value: capacity_used,
            threshold: @thresholds[:capacity_critical]
          }
        elsif capacity_used > @thresholds[:capacity_warning]
          analysis[:warnings] << "System capacity elevated: #{capacity_used}%"
        end
      end
      
      if active_users && active_users > @thresholds[:active_users_max]
        analysis[:alerts] << {
          type: 'max_users_exceeded',
          message: "Active users exceed limit: #{active_users}/#{@thresholds[:active_users_max]}",
          value: active_users,
          threshold: @thresholds[:active_users_max]
        }
      end
    end
    
    # System resources
    if metrics && metrics['system']
      memory_usage = metrics.dig('system', 'memory_usage')
      
      if memory_usage && memory_usage > @thresholds[:memory_warning]
        analysis[:warnings] << "Memory usage elevated: #{memory_usage.round(1)}MB"
      end
    end
    
    # Determine overall status
    if analysis[:alerts].any?
      analysis[:status] = 'critical'
    elsif analysis[:warnings].any? || analysis[:issues].any?
      analysis[:status] = 'warning'
    end
    
    analysis
  end
  
  def log_status(analysis, timestamp)
    status_line = "[#{timestamp.strftime('%Y-%m-%d %H:%M:%S')}] Status: #{analysis[:status].upcase}"
    
    if analysis[:alerts].any?
      status_line += " | Alerts: #{analysis[:alerts].size}"
    end
    
    if analysis[:warnings].any?
      status_line += " | Warnings: #{analysis[:warnings].size}"
    end
    
    if analysis[:issues].any?
      status_line += " | Issues: #{analysis[:issues].size}"
    end
    
    log(status_line)
  end
  
  def store_metrics_history(analysis, timestamp)
    history = load_metrics_history
    
    # Keep only last 100 entries to prevent log files from growing too large
    history = history.last(99) if history.size >= 100
    
    history << {
      timestamp: timestamp.iso8601,
      status: analysis[:status],
      alerts_count: analysis[:alerts].size,
      warnings_count: analysis[:warnings].size,
      issues_count: analysis[:issues].size
    }
    
    File.write(@metrics_history, JSON.pretty_generate(history))
  end
  
  def load_metrics_history
    if File.exist?(@metrics_history)
      JSON.parse(File.read(@metrics_history))
    else
      []
    end
  rescue
    []
  end
  
  def check_alerts(analysis, timestamp)
    analysis[:alerts].each do |alert|
      send_alert(alert, timestamp)
    end
  end
  
  def send_alert(alert, timestamp)
    alert_message = "[#{timestamp.strftime('%Y-%m-%d %H:%M:%S')}] ALERT: #{alert[:message]}"
    
    # Log the alert
    File.open(@alerts_log, 'a') do |f|
      f.puts alert_message
    end
    
    # Log to main log as well
    log(alert_message)
    
    # In production, this would send notifications via:
    # - Email
    # - Slack/Discord webhook
    # - PagerDuty/OpsGenie
    # - SMS for critical alerts
    
    puts "🚨 #{alert_message}" if ENV['VERBOSE']
  end
  
  def display_status(analysis)
    puts "\n" + "="*60
    puts "DataReflow.io Performance Status: #{analysis[:status].upcase}"
    puts "Time: #{analysis[:timestamp].strftime('%Y-%m-%d %H:%M:%S')}"
    puts "="*60
    
    if analysis[:alerts].any?
      puts "\n🚨 ALERTS:"
      analysis[:alerts].each { |alert| puts "  - #{alert[:message]}" }
    end
    
    if analysis[:warnings].any?
      puts "\n⚠️  WARNINGS:"
      analysis[:warnings].each { |warning| puts "  - #{warning}" }
    end
    
    if analysis[:issues].any?
      puts "\n❌ ISSUES:"
      analysis[:issues].each { |issue| puts "  - #{issue}" }
    end
    
    if analysis[:status] == 'healthy'
      puts "\n✅ All systems operating normally"
    end
    
    puts "="*60
  end
  
  def log(message)
    timestamp = Time.current.strftime('%Y-%m-%d %H:%M:%S')
    log_line = "[#{timestamp}] #{message}"
    
    File.open(@monitoring_log, 'a') do |f|
      f.puts log_line
    end
    
    puts log_line if ENV['VERBOSE'] || ENV['DEBUG']
  end
end

# Script execution
if __FILE__ == $0
  base_url = ARGV[0] || 'http://localhost:3000'
  interval = (ARGV[1] || 30).to_i
  mode = ARGV[2] || 'continuous'
  
  monitor = ContinuousMonitor.new(base_url)
  
  case mode
  when 'once'
    monitor.run_once
  when 'continuous'
    puts "Starting continuous monitoring of #{base_url}"
    puts "Monitoring interval: #{interval} seconds"
    puts "Press Ctrl+C to stop"
    puts "Set VERBOSE=1 for detailed output"
    puts ""
    
    monitor.start_monitoring(interval)
  else
    puts "Usage: ruby continuous_monitoring.rb [base_url] [interval_seconds] [mode]"
    puts "  base_url: Application URL (default: http://localhost:3000)"
    puts "  interval_seconds: Monitoring interval (default: 30)"
    puts "  mode: 'continuous' or 'once' (default: continuous)"
    puts ""
    puts "Examples:"
    puts "  ruby continuous_monitoring.rb                              # Monitor localhost every 30s"
    puts "  ruby continuous_monitoring.rb http://localhost:3000 60     # Monitor every 60s"
    puts "  ruby continuous_monitoring.rb http://localhost:3000 30 once # Single check"
    puts ""
    puts "Environment variables:"
    puts "  VERBOSE=1  - Show detailed output"
    puts "  DEBUG=1    - Show debug information"
  end
end