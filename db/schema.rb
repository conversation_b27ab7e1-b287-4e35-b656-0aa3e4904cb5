# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_08_09_153214) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "account_invitations", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "invited_by_id", null: false
    t.string "email"
    t.string "token"
    t.integer "role"
    t.integer "status"
    t.datetime "expires_at"
    t.datetime "accepted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_account_invitations_on_account_id"
    t.index ["invited_by_id"], name: "index_account_invitations_on_invited_by_id"
    t.index ["token"], name: "index_account_invitations_on_token"
  end

  create_table "accounts", force: :cascade do |t|
    t.string "name"
    t.string "subdomain"
    t.integer "status"
    t.jsonb "settings"
    t.datetime "onboarded_at"
    t.string "stripe_customer_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "onboarding_completed", default: false, null: false
    t.datetime "onboarding_completed_at"
    t.boolean "onboarding_welcome_completed", default: false, null: false
    t.boolean "onboarding_profile_completed", default: false, null: false
    t.boolean "onboarding_connection_completed", default: false, null: false
    t.boolean "onboarding_pipeline_completed", default: false, null: false
    t.boolean "onboarding_team_completed", default: false, null: false
    t.datetime "agent_last_recommendation_at"
    t.index ["onboarding_completed"], name: "index_accounts_on_onboarding_completed"
    t.index ["subdomain"], name: "index_accounts_on_subdomain", unique: true
  end

  create_table "agent_recommendations", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "pipeline_id"
    t.integer "recommendation_type", null: false
    t.string "title", null: false
    t.text "description"
    t.integer "status", default: 0
    t.decimal "estimated_value", precision: 10, scale: 2
    t.decimal "confidence_score", precision: 5, scale: 2
    t.integer "priority", default: 0
    t.jsonb "implementation_steps", default: {}
    t.jsonb "ai_analysis", default: {}
    t.jsonb "before_metrics", default: {}
    t.jsonb "after_metrics", default: {}
    t.integer "revenue_generated_cents", default: 0
    t.datetime "implemented_at"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "created_at"], name: "index_agent_recommendations_on_account_id_and_created_at"
    t.index ["account_id", "status"], name: "index_agent_recommendations_on_account_id_and_status"
    t.index ["account_id"], name: "index_agent_recommendations_on_account_id"
    t.index ["confidence_score"], name: "index_agent_recommendations_on_confidence_score"
    t.index ["expires_at"], name: "index_agent_recommendations_on_expires_at"
    t.index ["pipeline_id"], name: "index_agent_recommendations_on_pipeline_id"
    t.index ["priority"], name: "index_agent_recommendations_on_priority"
    t.index ["recommendation_type"], name: "index_agent_recommendations_on_recommendation_type"
  end

  create_table "agent_revenues", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "agent_recommendation_id"
    t.bigint "pipeline_id"
    t.integer "revenue_source", null: false
    t.integer "amount_cents", null: false
    t.string "currency", default: "USD"
    t.string "description"
    t.jsonb "performance_metrics", default: {}
    t.integer "billing_period"
    t.datetime "period_start"
    t.datetime "period_end"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "created_at"], name: "index_agent_revenues_on_account_id_and_created_at"
    t.index ["account_id", "revenue_source"], name: "index_agent_revenues_on_account_id_and_revenue_source"
    t.index ["account_id"], name: "index_agent_revenues_on_account_id"
    t.index ["agent_recommendation_id"], name: "index_agent_revenues_on_agent_recommendation_id"
    t.index ["period_start", "period_end"], name: "index_agent_revenues_on_period_start_and_period_end"
    t.index ["pipeline_id"], name: "index_agent_revenues_on_pipeline_id"
    t.index ["revenue_source"], name: "index_agent_revenues_on_revenue_source"
    t.index ["status"], name: "index_agent_revenues_on_status"
  end

  create_table "ahoy_events", force: :cascade do |t|
    t.bigint "visit_id"
    t.bigint "user_id"
    t.string "name"
    t.jsonb "properties"
    t.datetime "time"
    t.index ["name", "time"], name: "index_ahoy_events_on_name_and_time"
    t.index ["properties"], name: "index_ahoy_events_on_properties", opclass: :jsonb_path_ops, using: :gin
    t.index ["user_id"], name: "index_ahoy_events_on_user_id"
    t.index ["visit_id"], name: "index_ahoy_events_on_visit_id"
  end

  create_table "ahoy_visits", force: :cascade do |t|
    t.string "visit_token"
    t.string "visitor_token"
    t.bigint "user_id"
    t.string "ip"
    t.text "user_agent"
    t.text "referrer"
    t.string "referring_domain"
    t.text "landing_page"
    t.string "browser"
    t.string "os"
    t.string "device_type"
    t.string "country"
    t.string "region"
    t.string "city"
    t.float "latitude"
    t.float "longitude"
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_term"
    t.string "utm_content"
    t.string "utm_campaign"
    t.string "app_version"
    t.string "os_version"
    t.string "platform"
    t.datetime "started_at"
    t.index ["user_id"], name: "index_ahoy_visits_on_user_id"
    t.index ["visit_token"], name: "index_ahoy_visits_on_visit_token", unique: true
    t.index ["visitor_token", "started_at"], name: "index_ahoy_visits_on_visitor_token_and_started_at"
  end

  create_table "api_tokens", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "created_by_id", null: false
    t.string "name"
    t.string "token"
    t.datetime "last_used_at"
    t.datetime "expires_at"
    t.datetime "revoked_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_api_tokens_on_account_id"
    t.index ["created_by_id"], name: "index_api_tokens_on_created_by_id"
    t.index ["token"], name: "index_api_tokens_on_token"
  end

  create_table "data_connectors", force: :cascade do |t|
    t.bigint "created_by", null: false
    t.string "name", null: false
    t.string "connector_type", null: false
    t.jsonb "connection_config", default: {}, null: false
    t.integer "status", default: 0, null: false
    t.datetime "last_tested_at"
    t.text "test_result"
    t.integer "test_status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "project_id", null: false
    t.index ["connector_type"], name: "index_data_connectors_on_connector_type"
    t.index ["created_by"], name: "index_data_connectors_on_created_by"
    t.index ["project_id", "connector_type"], name: "index_data_connectors_on_project_id_and_connector_type"
    t.index ["project_id", "status"], name: "index_data_connectors_on_project_id_and_status"
    t.index ["project_id"], name: "index_data_connectors_on_project_id"
  end

  create_table "flipper_features", force: :cascade do |t|
    t.string "key", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_flipper_features_on_key", unique: true
  end

  create_table "flipper_gates", force: :cascade do |t|
    t.string "feature_key", null: false
    t.string "key", null: false
    t.text "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["feature_key", "key", "value"], name: "index_flipper_gates_on_feature_key_and_key_and_value", unique: true
  end

  create_table "jwt_denylists", force: :cascade do |t|
    t.string "jti"
    t.datetime "exp"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["jti"], name: "index_jwt_denylists_on_jti"
  end

  create_table "notifications", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "user_id"
    t.string "title", null: false
    t.text "message", null: false
    t.string "notification_type", null: false
    t.string "priority", default: "medium", null: false
    t.datetime "read_at"
    t.string "action_url"
    t.string "notifiable_type"
    t.bigint "notifiable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "created_at"], name: "index_notifications_on_account_id_and_created_at"
    t.index ["account_id", "notification_type"], name: "index_notifications_on_account_id_and_notification_type"
    t.index ["account_id", "priority"], name: "index_notifications_on_account_id_and_priority"
    t.index ["account_id", "read_at"], name: "index_notifications_on_account_id_and_read_at"
    t.index ["account_id", "user_id"], name: "index_notifications_on_account_id_and_user_id"
    t.index ["account_id"], name: "index_notifications_on_account_id"
    t.index ["notifiable_type", "notifiable_id"], name: "index_notifications_on_notifiable"
    t.index ["notifiable_type", "notifiable_id"], name: "index_notifications_on_notifiable_type_and_notifiable_id"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "pipeline_executions", force: :cascade do |t|
    t.bigint "pipeline_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "started_at", null: false
    t.datetime "completed_at"
    t.decimal "execution_time", precision: 10, scale: 3
    t.integer "records_processed", default: 0
    t.integer "records_success", default: 0
    t.integer "records_failed", default: 0
    t.text "error_message"
    t.text "execution_log"
    t.jsonb "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["pipeline_id", "created_at"], name: "index_pipeline_executions_on_pipeline_id_and_created_at"
    t.index ["pipeline_id", "started_at"], name: "index_pipeline_executions_on_pipeline_id_and_started_at"
    t.index ["pipeline_id", "status"], name: "index_pipeline_executions_on_pipeline_id_and_status"
    t.index ["pipeline_id"], name: "index_pipeline_executions_on_pipeline_id"
    t.index ["started_at"], name: "index_pipeline_executions_on_started_at"
    t.index ["status"], name: "index_pipeline_executions_on_status"
  end

  create_table "pipeline_templates", force: :cascade do |t|
    t.bigint "creator_account_id"
    t.bigint "source_pipeline_id"
    t.string "name", null: false
    t.text "description"
    t.string "category"
    t.string "industry"
    t.string "source_type", null: false
    t.string "destination_type", null: false
    t.jsonb "source_config_template", default: {}
    t.jsonb "destination_config_template", default: {}
    t.jsonb "transformation_template", default: {}
    t.jsonb "schedule_template", default: {}
    t.integer "price_cents", default: 0
    t.string "currency", default: "USD"
    t.integer "purchases_count", default: 0
    t.decimal "average_rating", precision: 3, scale: 2
    t.integer "ratings_count", default: 0
    t.jsonb "performance_metrics", default: {}
    t.jsonb "use_cases", default: []
    t.jsonb "requirements", default: []
    t.integer "status", default: 0
    t.boolean "featured", default: false
    t.datetime "published_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["average_rating"], name: "index_pipeline_templates_on_average_rating"
    t.index ["category"], name: "index_pipeline_templates_on_category"
    t.index ["creator_account_id"], name: "index_pipeline_templates_on_creator_account_id"
    t.index ["featured"], name: "index_pipeline_templates_on_featured"
    t.index ["industry"], name: "index_pipeline_templates_on_industry"
    t.index ["name"], name: "index_pipeline_templates_on_name"
    t.index ["purchases_count"], name: "index_pipeline_templates_on_purchases_count"
    t.index ["source_pipeline_id"], name: "index_pipeline_templates_on_source_pipeline_id"
    t.index ["source_type", "destination_type"], name: "index_pipeline_templates_on_source_type_and_destination_type"
    t.index ["status"], name: "index_pipeline_templates_on_status"
  end

  create_table "pipelines", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "created_by", null: false
    t.string "name", null: false
    t.text "description"
    t.integer "status", default: 0, null: false
    t.jsonb "source_config", default: {}, null: false
    t.jsonb "destination_config", default: {}, null: false
    t.jsonb "transformation_rules", default: {}
    t.integer "schedule_type", default: 0, null: false
    t.jsonb "schedule_config", default: {}
    t.integer "execution_count", default: 0, null: false
    t.datetime "last_executed_at"
    t.integer "last_execution_status"
    t.decimal "avg_execution_time", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "template_type"
    t.index ["account_id", "created_at"], name: "index_pipelines_on_account_id_and_created_at"
    t.index ["account_id", "status"], name: "index_pipelines_on_account_id_and_status"
    t.index ["account_id"], name: "index_pipelines_on_account_id"
    t.index ["created_by"], name: "index_pipelines_on_created_by"
    t.index ["last_executed_at"], name: "index_pipelines_on_last_executed_at"
    t.index ["schedule_type"], name: "index_pipelines_on_schedule_type"
  end

  create_table "projects", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "created_by_id", null: false
    t.string "name", limit: 100, null: false
    t.text "description"
    t.integer "status", default: 0, null: false
    t.jsonb "settings", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "created_at"], name: "index_projects_on_account_id_and_created_at"
    t.index ["account_id", "name"], name: "index_projects_on_account_id_and_name", unique: true
    t.index ["account_id", "status"], name: "index_projects_on_account_id_and_status"
    t.index ["account_id"], name: "index_projects_on_account_id"
    t.index ["created_by_id"], name: "index_projects_on_created_by_id"
  end

  create_table "stripe_webhooks", force: :cascade do |t|
    t.string "event_id"
    t.string "event_type"
    t.boolean "processed"
    t.datetime "processed_at"
    t.json "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "subscription_plans", force: :cascade do |t|
    t.string "name"
    t.string "stripe_product_id"
    t.string "stripe_price_id"
    t.integer "price_cents"
    t.string "billing_cycle"
    t.text "features"
    t.boolean "active"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "subscriptions", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.integer "plan"
    t.integer "status"
    t.string "stripe_subscription_id"
    t.string "stripe_status"
    t.datetime "current_period_start"
    t.datetime "current_period_end"
    t.boolean "cancel_at_period_end"
    t.datetime "trial_ends_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "stripe_customer_id"
    t.bigint "subscription_plan_id"
    t.datetime "trial_start"
    t.datetime "trial_end"
    t.datetime "canceled_at"
    t.index ["account_id"], name: "index_subscriptions_on_account_id"
    t.index ["subscription_plan_id"], name: "index_subscriptions_on_subscription_plan_id"
  end

  create_table "team_invitations", force: :cascade do |t|
    t.string "email"
    t.integer "role"
    t.bigint "invited_by_id", null: false
    t.bigint "account_id", null: false
    t.integer "status"
    t.string "token"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_team_invitations_on_account_id"
    t.index ["invited_by_id"], name: "index_team_invitations_on_invited_by_id"
  end

  create_table "usage_metrics", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.string "metric_type"
    t.decimal "value", precision: 10, scale: 2
    t.datetime "recorded_at"
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "metric_type"], name: "index_usage_metrics_on_account_id_and_metric_type"
    t.index ["account_id", "recorded_at"], name: "index_usage_metrics_on_account_id_and_recorded_at"
    t.index ["account_id"], name: "index_usage_metrics_on_account_id"
    t.index ["metric_type", "recorded_at"], name: "index_usage_metrics_on_metric_type_and_recorded_at"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.bigint "account_id", null: false
    t.string "first_name"
    t.string "last_name"
    t.integer "role", default: 2, null: false
    t.jsonb "settings", default: {}
    t.string "time_zone", default: "UTC"
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at"
    t.string "otp_secret"
    t.integer "consumed_timestep"
    t.boolean "otp_required_for_login", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "admin", default: false, null: false
    t.index ["account_id", "email"], name: "index_users_on_account_id_and_email", unique: true
    t.index ["account_id"], name: "index_users_on_account_id"
    t.index ["admin"], name: "index_users_on_admin"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true
  end

  add_foreign_key "account_invitations", "accounts"
  add_foreign_key "account_invitations", "users", column: "invited_by_id"
  add_foreign_key "agent_recommendations", "accounts"
  add_foreign_key "agent_recommendations", "pipelines"
  add_foreign_key "agent_revenues", "accounts"
  add_foreign_key "agent_revenues", "agent_recommendations"
  add_foreign_key "agent_revenues", "pipelines"
  add_foreign_key "api_tokens", "accounts"
  add_foreign_key "api_tokens", "users", column: "created_by_id"
  add_foreign_key "data_connectors", "projects"
  add_foreign_key "data_connectors", "users", column: "created_by"
  add_foreign_key "notifications", "accounts"
  add_foreign_key "notifications", "users"
  add_foreign_key "pipeline_executions", "pipelines"
  add_foreign_key "pipeline_templates", "accounts", column: "creator_account_id"
  add_foreign_key "pipeline_templates", "pipelines", column: "source_pipeline_id"
  add_foreign_key "pipelines", "accounts"
  add_foreign_key "pipelines", "users", column: "created_by"
  add_foreign_key "projects", "accounts"
  add_foreign_key "projects", "users", column: "created_by_id"
  add_foreign_key "subscriptions", "accounts"
  add_foreign_key "subscriptions", "subscription_plans"
  add_foreign_key "team_invitations", "accounts"
  add_foreign_key "team_invitations", "users", column: "invited_by_id"
  add_foreign_key "usage_metrics", "accounts"
  add_foreign_key "users", "accounts"
end
