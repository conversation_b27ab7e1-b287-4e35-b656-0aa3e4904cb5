module ApplicationHelper
  def nav_link_classes(active = false)
    base_classes = "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150"
    if active
      "#{base_classes} bg-indigo-50 border-r-2 border-indigo-500 text-indigo-700"
    else
      "#{base_classes} text-gray-700 hover:bg-gray-50 hover:text-gray-900"
    end
  end

  # Role-based UI helpers
  def can_manage_account?(user = current_user)
    user&.can_manage_account?
  end

  def can_manage_team?(user = current_user)
    user&.can_manage_team?
  end

  def can_execute_pipelines?(user = current_user)
    user&.can_execute_pipelines?
  end

  def show_for_roles(*roles, user: current_user)
    return false unless user
    roles.map(&:to_s).include?(user.role)
  end

  # Plan-based feature helpers
  def feature_available?(feature, account = current_account)
    return false unless account&.subscription

    case feature.to_sym
    when :advanced_analytics
      %w[professional enterprise].include?(account.subscription.plan)
    when :team_management
      %w[starter professional enterprise].include?(account.subscription.plan)
    when :api_access
      %w[professional enterprise].include?(account.subscription.plan)
    when :custom_integrations
      account.subscription.plan == "enterprise"
    when :priority_support
      %w[professional enterprise].include?(account.subscription.plan)
    else
      true
    end
  end

  # Tenant customization helpers
  def account_branding_color(account = current_account)
    account&.settings&.dig("branding", "primary_color") || "#4F46E5"
  end

  def account_logo_url(account = current_account)
    account&.settings&.dig("branding", "logo_url") || nil
  end

  def account_custom_domain(account = current_account)
    account&.settings&.dig("domain", "custom_domain") || nil
  end

  # Usage limit helpers
  def usage_percentage(current, limit)
    return 0 if limit.zero? || limit == -1
    [ (current.to_f / limit * 100).round(1), 100 ].min
  end

  def usage_status_class(percentage)
    case percentage
    when 0..70
      "text-green-600 bg-green-100"
    when 71..90
      "text-yellow-600 bg-yellow-100"
    else
      "text-red-600 bg-red-100"
    end
  end

  # Business metrics formatting
  def format_metric_value(value, type = :number)
    case type
    when :currency
      number_to_currency(value)
    when :percentage
      number_to_percentage(value, precision: 1)
    when :bytes
      number_to_human_size(value)
    when :duration
      format_duration(value)
    when :compact
      number_to_human(value, precision: 1)
    else
      number_with_delimiter(value)
    end
  end

  def format_duration(seconds)
    return "0s" if seconds.zero?

    if seconds < 60
      "#{seconds.round}s"
    elsif seconds < 3600
      "#{(seconds / 60).round}m"
    else
      hours = seconds / 3600
      minutes = (seconds % 3600) / 60
      "#{hours.round}h #{minutes.round}m"
    end
  end

  # Status indicators
  def status_badge(status, text = nil)
    text ||= status.to_s.humanize

    case status.to_s.downcase
    when "active", "healthy", "success", "completed"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
    when "warning", "pending", "in_progress"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
    when "error", "failed", "inactive", "suspended"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
    else
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
    end
  end
end
