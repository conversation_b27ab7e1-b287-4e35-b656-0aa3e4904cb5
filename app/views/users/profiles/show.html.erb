<%# Sophisticated User Profile Management Interface %>
<%# Demonstrates DataReflow's design system with advanced UI patterns %>

<% content_for :title, "Profile Settings - #{@user.full_name}" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-50" 
     data-controller="profile-manager tooltip"
     data-profile-manager-auto-save-value="true"
     data-profile-manager-validation-enabled-value="true">
  
  <!-- <PERSON> Header with Breadcrumb -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <!-- Breadcrumb Navigation -->
        <nav class="flex mb-4" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <%= link_to root_path,
                  class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-150" do %>
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                </svg>
                Dashboard
              <% end %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Profile Settings</span>
              </div>
            </li>
          </ol>
        </nav>

        <!-- Page Title and Actions -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div class="flex items-center space-x-4">
            <!-- User Avatar with Status Indicator -->
            <div class="relative">
              <div class="h-16 w-16 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg ring-4 ring-white">
                <span class="text-xl font-bold text-white">
                  <%= @user.full_name.split.map(&:first).join.upcase %>
                </span>
              </div>
              <!-- Online Status Indicator -->
              <div class="absolute -bottom-1 -right-1 h-5 w-5 bg-emerald-400 border-2 border-white rounded-full"
                   data-controller="tooltip"
                   data-tooltip-text-value="Online"
                   data-tooltip-position-value="top">
              </div>
            </div>
            
            <div>
              <h1 class="text-3xl font-bold text-gray-900">
                <%= @user.full_name %>
              </h1>
              <div class="flex items-center space-x-3 mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                  <%= @user.role.humanize %>
                </span>
                <span class="text-sm text-gray-500">
                  Member since <%= @user.created_at.strftime('%B %Y') %>
                </span>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="mt-4 sm:mt-0 flex space-x-3">
            <button type="button" 
                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150"
                    data-action="click->profile-manager#exportProfile"
                    data-controller="tooltip"
                    data-tooltip-text-value="Download your profile data">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              Export Data
            </button>
            
            <button type="button" 
                    class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transform hover:scale-105 transition-all duration-150"
                    data-action="click->profile-manager#saveProfile"
                    data-profile-manager-target="saveButton">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              <span data-profile-manager-target="saveText">Save Changes</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
      
      <!-- Sidebar Navigation -->
      <div class="lg:col-span-1">
        <nav class="space-y-1" aria-label="Profile sections">
          <a href="#personal-info" 
             class="bg-indigo-50 border-r-2 border-indigo-500 text-indigo-700 group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-150"
             data-action="click->profile-manager#navigateToSection"
             data-section="personal-info">
            <svg class="text-indigo-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
            Personal Information
          </a>
          
          <a href="#security" 
             class="text-gray-700 hover:text-gray-900 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-150"
             data-action="click->profile-manager#navigateToSection"
             data-section="security">
            <svg class="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
            </svg>
            Security & Privacy
          </a>
          
          <a href="#notifications" 
             class="text-gray-700 hover:text-gray-900 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-150"
             data-action="click->profile-manager#navigateToSection"
             data-section="notifications">
            <svg class="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"/>
            </svg>
            Notifications
          </a>
          
          <a href="#preferences" 
             class="text-gray-700 hover:text-gray-900 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-150"
             data-action="click->profile-manager#navigateToSection"
             data-section="preferences">
            <svg class="text-gray-400 group-hover:text-gray-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            Preferences
          </a>
        </nav>
      </div>

      <!-- Main Content -->
      <div class="lg:col-span-3">
        <!-- Auto-save Indicator -->
        <div class="mb-6">
          <div class="hidden bg-green-50 border border-green-200 rounded-md p-3"
               data-profile-manager-target="autoSaveIndicator">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-green-800">
                  Changes saved automatically
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Personal Information Section -->
        <section id="personal-info" class="mb-8" aria-labelledby="personal-info-heading">
          <div class="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-4 border-b border-gray-200">
              <h2 id="personal-info-heading" class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                Personal Information
              </h2>
              <p class="mt-1 text-sm text-gray-600">
                Update your personal details and profile information.
              </p>
            </div>

            <div class="p-6">
              <%= form_with model: @user, url: users_profile_path, method: :patch,
                  class: "space-y-6",
                  data: {
                    controller: "form-validation",
                    action: "input->profile-manager#handleInput change->profile-manager#handleChange",
                    profile_manager_target: "form"
                  } do |form| %>

                <!-- Name Fields -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div class="space-y-1">
                    <%= form.label :first_name, class: "block text-sm font-medium text-gray-700" do %>
                      First Name
                      <span class="text-red-500" aria-label="required">*</span>
                    <% end %>
                    <%= form.text_field :first_name,
                        class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                        placeholder: "Enter your first name",
                        required: true,
                        data: {
                          action: "input->form-validation#validateField blur->form-validation#validateField",
                          form_validation_target: "field"
                        } %>
                    <div class="hidden mt-1 text-sm text-red-600" data-form-validation-target="error" data-field="first_name">
                      <!-- Error message will be inserted here -->
                    </div>
                  </div>

                  <div class="space-y-1">
                    <%= form.label :last_name, class: "block text-sm font-medium text-gray-700" do %>
                      Last Name
                      <span class="text-red-500" aria-label="required">*</span>
                    <% end %>
                    <%= form.text_field :last_name,
                        class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                        placeholder: "Enter your last name",
                        required: true,
                        data: {
                          action: "input->form-validation#validateField blur->form-validation#validateField",
                          form_validation_target: "field"
                        } %>
                    <div class="hidden mt-1 text-sm text-red-600" data-form-validation-target="error" data-field="last_name">
                      <!-- Error message will be inserted here -->
                    </div>
                  </div>
                </div>

                <!-- Email Field -->
                <div class="space-y-1">
                  <%= form.label :email, class: "block text-sm font-medium text-gray-700" do %>
                    Email Address
                    <span class="text-red-500" aria-label="required">*</span>
                  <% end %>
                  <div class="relative">
                    <%= form.email_field :email,
                        class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm pl-10 transition-all duration-150",
                        placeholder: "<EMAIL>",
                        required: true,
                        data: {
                          action: "input->form-validation#validateField blur->form-validation#validateField",
                          form_validation_target: "field"
                        } %>
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                      </svg>
                    </div>
                  </div>
                  <div class="hidden mt-1 text-sm text-red-600" data-form-validation-target="error" data-field="email">
                    <!-- Error message will be inserted here -->
                  </div>
                  <p class="mt-1 text-sm text-gray-500">
                    We'll send important account notifications to this email address.
                  </p>
                </div>

                <!-- Phone Number Field -->
                <div class="space-y-1">
                  <%= form.label :phone, class: "block text-sm font-medium text-gray-700" do %>
                    Phone Number
                    <span class="text-gray-400 text-xs">(Optional)</span>
                  <% end %>
                  <div class="relative">
                    <%= form.telephone_field :phone,
                        class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm pl-10 transition-all duration-150",
                        placeholder: "+****************",
                        data: {
                          action: "input->form-validation#validateField blur->form-validation#validateField",
                          form_validation_target: "field"
                        } %>
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                      </svg>
                    </div>
                  </div>
                  <div class="hidden mt-1 text-sm text-red-600" data-form-validation-target="error" data-field="phone">
                    <!-- Error message will be inserted here -->
                  </div>
                </div>

                <!-- Bio/Description Field -->
                <div class="space-y-1">
                  <%= form.label :bio, class: "block text-sm font-medium text-gray-700" do %>
                    Bio
                    <span class="text-gray-400 text-xs">(Optional)</span>
                  <% end %>
                  <%= form.text_area :bio,
                      rows: 4,
                      class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                      placeholder: "Tell us a bit about yourself and your role...",
                      data: {
                        action: "input->form-validation#validateField",
                        form_validation_target: "field"
                      } %>
                  <p class="mt-1 text-sm text-gray-500">
                    Brief description for your team members. Maximum 500 characters.
                  </p>
                </div>

              <% end %>
            </div>
          </div>
        </section>

        <!-- Security & Privacy Section -->
        <section id="security" class="mb-8" aria-labelledby="security-heading">
          <div class="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-red-50 to-orange-50 px-6 py-4 border-b border-gray-200">
              <h2 id="security-heading" class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                </svg>
                Security & Privacy
              </h2>
              <p class="mt-1 text-sm text-gray-600">
                Manage your account security settings and privacy preferences.
              </p>
            </div>

            <div class="p-6 space-y-8">
              <!-- Password Change -->
              <div class="border-b border-gray-200 pb-8">
                <h3 class="text-base font-medium text-gray-900 mb-4">Change Password</h3>
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                      </svg>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm text-gray-700">
                        <strong>Password Requirements:</strong> At least 8 characters with a mix of letters, numbers, and symbols.
                      </p>
                    </div>
                  </div>
                </div>

                <%= form_with url: change_password_users_profile_path, method: :patch,
                    class: "space-y-4",
                    data: {
                      controller: "password-strength",
                      action: "submit->profile-manager#changePassword"
                    } do |form| %>

                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div class="space-y-1">
                      <%= form.label :current_password, class: "block text-sm font-medium text-gray-700" do %>
                        Current Password
                        <span class="text-red-500" aria-label="required">*</span>
                      <% end %>
                      <%= form.password_field :current_password,
                          class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                          required: true,
                          autocomplete: "current-password" %>
                    </div>

                    <div class="space-y-1">
                      <%= form.label :new_password, class: "block text-sm font-medium text-gray-700" do %>
                        New Password
                        <span class="text-red-500" aria-label="required">*</span>
                      <% end %>
                      <%= form.password_field :new_password,
                          class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                          required: true,
                          autocomplete: "new-password",
                          data: {
                            action: "input->password-strength#checkStrength",
                            password_strength_target: "password"
                          } %>

                      <!-- Password Strength Indicator -->
                      <div class="mt-2" data-password-strength-target="indicator">
                        <div class="flex items-center space-x-2">
                          <div class="flex-1 bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full transition-all duration-300"
                                 data-password-strength-target="bar"></div>
                          </div>
                          <span class="text-xs font-medium text-gray-500"
                                data-password-strength-target="label">Enter password</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="space-y-1">
                    <%= form.label :password_confirmation, class: "block text-sm font-medium text-gray-700" do %>
                      Confirm New Password
                      <span class="text-red-500" aria-label="required">*</span>
                    <% end %>
                    <%= form.password_field :password_confirmation,
                        class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                        required: true,
                        autocomplete: "new-password",
                        data: {
                          action: "input->password-strength#checkMatch",
                          password_strength_target: "confirmation"
                        } %>
                    <div class="hidden mt-1 text-sm text-red-600" data-password-strength-target="matchError">
                      Passwords do not match
                    </div>
                  </div>

                  <div class="flex justify-end">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-150"
                            data-password-strength-target="submitButton"
                            disabled>
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                      </svg>
                      Update Password
                    </button>
                  </div>
                <% end %>
              </div>

              <!-- Two-Factor Authentication -->
              <div class="border-b border-gray-200 pb-8">
                <div class="flex items-center justify-between mb-4">
                  <div>
                    <h3 class="text-base font-medium text-gray-900">Two-Factor Authentication</h3>
                    <p class="text-sm text-gray-600">Add an extra layer of security to your account.</p>
                  </div>
                  <div class="flex items-center">
                    <% if @user.two_factor_enabled? %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        Enabled
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                        </svg>
                        Disabled
                      </span>
                    <% end %>
                  </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                      </svg>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm text-blue-700">
                        Two-factor authentication adds an extra layer of security by requiring a verification code from your phone in addition to your password.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="flex space-x-3">
                  <% if @user.two_factor_enabled? %>
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-150"
                            data-action="click->profile-manager#disableTwoFactor">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                      </svg>
                      Disable 2FA
                    </button>
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150"
                            data-action="click->profile-manager#showBackupCodes">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                      </svg>
                      View Backup Codes
                    </button>
                  <% else %>
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-150"
                            data-action="click->profile-manager#enableTwoFactor">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                      </svg>
                      Enable 2FA
                    </button>
                  <% end %>
                </div>
              </div>

              <!-- Session Management -->
              <div>
                <h3 class="text-base font-medium text-gray-900 mb-4">Active Sessions</h3>
                <p class="text-sm text-gray-600 mb-4">
                  Manage devices and browsers that are currently signed in to your account.
                </p>

                <div class="space-y-3">
                  <!-- Current Session -->
                  <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                          </svg>
                        </div>
                        <div>
                          <p class="text-sm font-medium text-green-900">Current Session</p>
                          <p class="text-xs text-green-700">Chrome on macOS • San Francisco, CA</p>
                        </div>
                      </div>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active Now
                      </span>
                    </div>
                  </div>

                  <!-- Other Sessions -->
                  <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                          </svg>
                        </div>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Mobile App</p>
                          <p class="text-xs text-gray-500">iPhone • Last active 2 hours ago</p>
                        </div>
                      </div>
                      <button type="button"
                              class="text-red-600 hover:text-red-900 text-sm font-medium transition-colors duration-150"
                              data-action="click->profile-manager#revokeSession"
                              data-session-id="mobile-123">
                        Revoke
                      </button>
                    </div>
                  </div>
                </div>

                <div class="mt-4">
                  <button type="button"
                          class="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-150"
                          data-action="click->profile-manager#revokeAllSessions">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                    </svg>
                    Sign Out All Other Sessions
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Notifications Section -->
        <section id="notifications" class="mb-8" aria-labelledby="notifications-heading">
          <div class="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-yellow-50 to-amber-50 px-6 py-4 border-b border-gray-200">
              <h2 id="notifications-heading" class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"/>
                </svg>
                Notification Preferences
              </h2>
              <p class="mt-1 text-sm text-gray-600">
                Choose how and when you want to receive notifications.
              </p>
            </div>

            <div class="p-6">
              <%= form_with model: @user, url: notification_preferences_users_profile_path, method: :patch,
                  class: "space-y-6",
                  data: {
                    controller: "notification-preferences",
                    action: "change->profile-manager#updateNotificationPreferences"
                  } do |form| %>

                <!-- Email Notifications -->
                <div class="space-y-4">
                  <h3 class="text-base font-medium text-gray-900">Email Notifications</h3>

                  <div class="space-y-3">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Pipeline Status Updates</p>
                          <p class="text-xs text-gray-500">Get notified when your pipelines succeed, fail, or need attention</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[email_pipeline_notifications]" value="0">
                        <%= form.check_box :email_pipeline_notifications,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->notification-preferences#updateSetting"
                            } %>
                      </div>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Team Activity</p>
                          <p class="text-xs text-gray-500">Updates when team members join, leave, or change roles</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[email_team_notifications]" value="0">
                        <%= form.check_box :email_team_notifications,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->notification-preferences#updateSetting"
                            } %>
                      </div>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Account & Billing</p>
                          <p class="text-xs text-gray-500">Important account updates, billing notifications, and security alerts</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[email_account_notifications]" value="0">
                        <%= form.check_box :email_account_notifications,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->notification-preferences#updateSetting"
                            } %>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- In-App Notifications -->
                <div class="space-y-4 border-t border-gray-200 pt-6">
                  <h3 class="text-base font-medium text-gray-900">In-App Notifications</h3>

                  <div class="space-y-3">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Desktop Notifications</p>
                          <p class="text-xs text-gray-500">Show browser notifications for important updates</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[desktop_notifications]" value="0">
                        <%= form.check_box :desktop_notifications,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->notification-preferences#updateSetting"
                            } %>
                      </div>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM6 6v12h8V6H6zm3 3a1 1 0 112 0v6a1 1 0 11-2 0V9z"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Sound Alerts</p>
                          <p class="text-xs text-gray-500">Play sound for critical notifications</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[sound_notifications]" value="0">
                        <%= form.check_box :sound_notifications,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->notification-preferences#updateSetting"
                            } %>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Notification Frequency -->
                <div class="space-y-4 border-t border-gray-200 pt-6">
                  <h3 class="text-base font-medium text-gray-900">Notification Frequency</h3>

                  <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                      <input id="frequency-immediate" name="user[notification_frequency]" type="radio" value="immediate"
                             class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                             <%= 'checked' if @user.notification_frequency == 'immediate' %>>
                      <label for="frequency-immediate" class="text-sm font-medium text-gray-700">
                        Immediate - Get notified right away
                      </label>
                    </div>

                    <div class="flex items-center space-x-3">
                      <input id="frequency-hourly" name="user[notification_frequency]" type="radio" value="hourly"
                             class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                             <%= 'checked' if @user.notification_frequency == 'hourly' %>>
                      <label for="frequency-hourly" class="text-sm font-medium text-gray-700">
                        Hourly - Batch notifications every hour
                      </label>
                    </div>

                    <div class="flex items-center space-x-3">
                      <input id="frequency-daily" name="user[notification_frequency]" type="radio" value="daily"
                             class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                             <%= 'checked' if @user.notification_frequency == 'daily' %>>
                      <label for="frequency-daily" class="text-sm font-medium text-gray-700">
                        Daily - Daily digest at 9:00 AM
                      </label>
                    </div>
                  </div>
                </div>

              <% end %>
            </div>
          </div>
        </section>

        <!-- Preferences Section -->
        <section id="preferences" class="mb-8" aria-labelledby="preferences-heading">
          <div class="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
              <h2 id="preferences-heading" class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                Application Preferences
              </h2>
              <p class="mt-1 text-sm text-gray-600">
                Customize your DataReflow experience and interface settings.
              </p>
            </div>

            <div class="p-6">
              <%= form_with model: @user, url: preferences_users_profile_path, method: :patch,
                  class: "space-y-6",
                  data: {
                    controller: "preferences",
                    action: "change->profile-manager#updatePreferences"
                  } do |form| %>

                <!-- Interface Preferences -->
                <div class="space-y-4">
                  <h3 class="text-base font-medium text-gray-900">Interface & Display</h3>

                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div class="space-y-1">
                      <%= form.label :theme, class: "block text-sm font-medium text-gray-700" %>
                      <%= form.select :theme,
                          options_for_select([
                            ['Light Theme', 'light'],
                            ['Dark Theme', 'dark'],
                            ['Auto (System)', 'auto']
                          ], @user.theme || 'light'),
                          {},
                          {
                            class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                            data: { action: "change->preferences#updateTheme" }
                          } %>
                    </div>

                    <div class="space-y-1">
                      <%= form.label :language, class: "block text-sm font-medium text-gray-700" %>
                      <%= form.select :language,
                          options_for_select([
                            ['English', 'en'],
                            ['Spanish', 'es'],
                            ['French', 'fr'],
                            ['German', 'de']
                          ], @user.language || 'en'),
                          {},
                          {
                            class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                            data: { action: "change->preferences#updateLanguage" }
                          } %>
                    </div>

                    <div class="space-y-1">
                      <%= form.label :timezone, class: "block text-sm font-medium text-gray-700" %>
                      <%= form.select :timezone,
                          options_for_select([
                            ['Pacific Time (PT)', 'America/Los_Angeles'],
                            ['Mountain Time (MT)', 'America/Denver'],
                            ['Central Time (CT)', 'America/Chicago'],
                            ['Eastern Time (ET)', 'America/New_York'],
                            ['UTC', 'UTC']
                          ], @user.timezone || 'UTC'),
                          {},
                          {
                            class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                            data: { action: "change->preferences#updateTimezone" }
                          } %>
                    </div>

                    <div class="space-y-1">
                      <%= form.label :date_format, class: "block text-sm font-medium text-gray-700" %>
                      <%= form.select :date_format,
                          options_for_select([
                            ['MM/DD/YYYY', 'US'],
                            ['DD/MM/YYYY', 'EU'],
                            ['YYYY-MM-DD', 'ISO']
                          ], @user.date_format || 'US'),
                          {},
                          {
                            class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-all duration-150",
                            data: { action: "change->preferences#updateDateFormat" }
                          } %>
                    </div>
                  </div>
                </div>

                <!-- Dashboard Preferences -->
                <div class="space-y-4 border-t border-gray-200 pt-6">
                  <h3 class="text-base font-medium text-gray-900">Dashboard Settings</h3>

                  <div class="space-y-3">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Auto-refresh Dashboard</p>
                          <p class="text-xs text-gray-500">Automatically update metrics every 30 seconds</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[auto_refresh_dashboard]" value="0">
                        <%= form.check_box :auto_refresh_dashboard,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->preferences#updateAutoRefresh"
                            } %>
                      </div>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Show Advanced Metrics</p>
                          <p class="text-xs text-gray-500">Display detailed performance and usage statistics</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[show_advanced_metrics]" value="0">
                        <%= form.check_box :show_advanced_metrics,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->preferences#updateAdvancedMetrics"
                            } %>
                      </div>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM6 6v12h8V6H6zm3 3a1 1 0 112 0v6a1 1 0 11-2 0V9z"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Compact View</p>
                          <p class="text-xs text-gray-500">Use a more condensed layout to show more information</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[compact_view]" value="0">
                        <%= form.check_box :compact_view,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->preferences#updateCompactView"
                            } %>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Privacy Preferences -->
                <div class="space-y-4 border-t border-gray-200 pt-6">
                  <h3 class="text-base font-medium text-gray-900">Privacy & Analytics</h3>

                  <div class="space-y-3">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Usage Analytics</p>
                          <p class="text-xs text-gray-500">Help improve DataReflow by sharing anonymous usage data</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[allow_analytics]" value="0">
                        <%= form.check_box :allow_analytics,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->preferences#updateAnalytics"
                            } %>
                      </div>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-center space-x-3">
                        <svg class="h-5 w-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                          <p class="text-sm font-medium text-gray-900">Product Updates</p>
                          <p class="text-xs text-gray-500">Receive information about new features and improvements</p>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <input type="hidden" name="user[product_updates]" value="0">
                        <%= form.check_box :product_updates,
                            class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                            data: {
                              controller: "toggle-switch",
                              action: "change->preferences#updateProductUpdates"
                            } %>
                      </div>
                    </div>
                  </div>
                </div>

              <% end %>
            </div>
          </div>
        </section>

      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
       data-profile-manager-target="loadingOverlay">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100">
          <svg class="animate-spin h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Saving Changes</h3>
        <div class="mt-2 px-7 py-3">
          <p class="text-sm text-gray-500">
            Please wait while we update your profile...
          </p>
        </div>
      </div>
    </div>
  </div>

</div>
