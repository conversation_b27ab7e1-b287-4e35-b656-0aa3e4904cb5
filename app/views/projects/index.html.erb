<% content_for :title, "Projects - DataReflow" %>

<div class="space-y-6">
  <!-- Header Section -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="min-w-0 flex-1">
      <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
        Projects
      </h1>
      <p class="mt-1 text-sm text-gray-500">
        Organize your data connectors into projects for better management
      </p>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0">
      <%= link_to new_project_path,
          class: "inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" do %>
        <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
        </svg>
        New Project
      <% end %>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
    <!-- Total Projects -->
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Total Projects</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @project_stats[:total_projects] %>
      </dd>
    </div>

    <!-- Active Projects -->
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Active Projects</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @project_stats[:active_projects] %>
      </dd>
    </div>

    <!-- Total Connectors -->
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Total Connectors</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @project_stats[:total_connectors] %>
      </dd>
    </div>

    <!-- Active Connectors -->
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Active Connectors</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @project_stats[:active_connectors] %>
      </dd>
    </div>
  </div>

  <!-- Projects List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @projects.any? %>
      <ul class="divide-y divide-gray-200">
        <% @projects.each do |project| %>
          <li class="hover:bg-gray-50">
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <div class="text-sm font-medium text-gray-900">
                      <%= project.name %>
                    </div>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= project.active? ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
                      <%= project.status.humanize %>
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">
                    <%= pluralize(project.connector_count, 'connector') %> • 
                    Created <%= time_ago_in_words(project.created_at) %> ago
                    <% if project.description.present? %>
                      • <%= truncate(project.description, length: 60) %>
                    <% end %>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <%= link_to project_path(project),
                    class: "text-indigo-600 hover:text-indigo-900 text-sm font-medium" do %>
                  View
                <% end %>
                <%= link_to edit_project_path(project),
                    class: "text-gray-600 hover:text-gray-900 text-sm font-medium" do %>
                  Edit
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <h3 class="mt-2 text-sm font-semibold text-gray-900">No projects</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating your first project.</p>
        <div class="mt-6">
          <%= link_to new_project_path,
              class: "inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" do %>
            <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            New Project
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
