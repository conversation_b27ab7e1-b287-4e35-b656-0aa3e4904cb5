<% content_for :title, "#{@project.name} - DataReflow" %>

<div class="space-y-6">
  <!-- Header Section -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="min-w-0 flex-1">
      <!-- Breadcrumb -->
      <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <%= link_to projects_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600" do %>
              <svg class="mr-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Projects
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2"><%= @project.name %></span>
            </div>
          </li>
        </ol>
      </nav>

      <div class="flex items-center">
        <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
          <%= @project.name %>
        </h1>
        <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium <%= @project.active? ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
          <%= @project.status.humanize %>
        </span>
      </div>
      <% if @project.description.present? %>
        <p class="mt-1 text-sm text-gray-500">
          <%= @project.description %>
        </p>
      <% end %>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
      <%= link_to new_project_data_connector_path(@project),
          class: "inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" do %>
        <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
        </svg>
        Add Connector
      <% end %>
      <%= link_to edit_project_path(@project),
          class: "inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
        <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
        </svg>
        Edit Project
      <% end %>
    </div>
  </div>

  <!-- Health Summary Cards -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
    <!-- Total Connectors -->
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Total Connectors</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @health_summary[:total_connectors] %>
      </dd>
    </div>

    <!-- Active Connectors -->
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Active Connectors</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-green-600">
        <%= @health_summary[:active_connectors] %>
      </dd>
    </div>

    <!-- Healthy Connectors -->
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Healthy Connectors</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-green-600">
        <%= @health_summary[:healthy_connectors] %>
      </dd>
    </div>

    <!-- Needs Attention -->
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Needs Attention</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-red-600">
        <%= @health_summary[:needs_attention] %>
      </dd>
    </div>
  </div>

  <!-- Data Connectors Section -->
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900">Data Connectors</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Manage data connections for this project
          </p>
        </div>
        <%= link_to project_data_connectors_path(@project),
            class: "text-indigo-600 hover:text-indigo-900 text-sm font-medium" do %>
          View All Connectors
        <% end %>
      </div>
    </div>

    <% if @data_connectors.any? %>
      <ul class="divide-y divide-gray-200">
        <% @data_connectors.each do |connector| %>
          <li class="hover:bg-gray-50">
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <div class="flex items-center">
                    <div class="text-sm font-medium text-gray-900">
                      <%= connector.name %>
                    </div>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= connector.active? ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
                      <%= connector.status.humanize %>
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">
                    <%= connector.connector_type.humanize %> • 
                    Last updated <%= time_ago_in_words(connector.updated_at) %> ago
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <%= link_to project_data_connector_path(@project, connector),
                    class: "text-indigo-600 hover:text-indigo-900 text-sm font-medium" do %>
                  View
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
        </svg>
        <h3 class="mt-2 text-sm font-semibold text-gray-900">No data connectors</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding your first data connector to this project.</p>
        <div class="mt-6">
          <%= link_to new_project_data_connector_path(@project),
              class: "inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" do %>
            <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            Add Connector
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
