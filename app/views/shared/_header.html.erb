<header class="bg-white shadow-sm border-b border-gray-200">
  <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
    <!-- Mobile menu button -->
    <div class="flex items-center lg:hidden">
      <button type="button" 
              class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
              data-action="click->navigation#toggleMobile"
              data-navigation-target="mobileButton">
        <span class="sr-only">Open main menu</span>
        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
        </svg>
      </button>
    </div>

    <!-- Page title and breadcrumb -->
    <div class="flex-1 min-w-0 lg:ml-0 ml-4">
      <div class="flex items-center">
        <h1 class="text-lg font-semibold text-gray-900 truncate">
          <%= content_for(:page_title) || "Dashboard" %>
        </h1>
        <% if content_for?(:breadcrumb) %>
          <nav class="hidden sm:flex ml-4" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
              <%= yield :breadcrumb %>
            </ol>
          </nav>
        <% end %>
      </div>
    </div>

    <!-- Right side items -->
    <div class="flex items-center space-x-4">
      <!-- Notifications -->
      <div class="relative"
           data-controller="dropdown notifications"
           data-notifications-url-value="/notifications/unread"
           data-notifications-count-url-value="/notifications/count">
        <button type="button"
                class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500"
                data-action="click->dropdown#toggle click->notifications#loadNotifications"
                data-dropdown-target="button"
                aria-label="View notifications">
          <span class="sr-only">View notifications</span>
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"/>
          </svg>
          <!-- Notification badge -->
          <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"
                data-notifications-target="badge"
                style="display: none;"></span>
        </button>

        <!-- Notifications dropdown -->
        <div class="hidden absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
             data-dropdown-target="menu"
             role="menu"
             aria-labelledby="notifications-button">
          <div class="py-1">
            <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-900">Notifications</h3>
              <button type="button"
                      class="text-xs text-indigo-600 hover:text-indigo-500"
                      data-action="click->notifications#markAllAsRead"
                      data-notifications-target="markAllButton">
                Mark all read
              </button>
            </div>
            <div class="max-h-64 overflow-y-auto" data-notifications-target="container">
              <!-- Loading state -->
              <div class="px-4 py-8 text-center" data-notifications-target="loading">
                <div class="inline-block w-6 h-6 border-2 border-gray-300 border-t-indigo-600 rounded-full animate-spin"></div>
                <p class="mt-2 text-sm text-gray-500">Loading notifications...</p>
              </div>

              <!-- Empty state -->
              <div class="px-4 py-8 text-center hidden" data-notifications-target="empty">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"/>
                </svg>
                <p class="mt-2 text-sm text-gray-500">No new notifications</p>
              </div>

              <!-- Notifications list -->
              <div data-notifications-target="list"></div>
            </div>

            <!-- Footer -->
            <div class="border-t border-gray-200 px-4 py-3">
              <%= link_to notifications_path,
                  class: "text-sm text-indigo-600 hover:text-indigo-500 font-medium" do %>
                View all notifications →
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- User menu -->
      <div class="relative" data-controller="dropdown">
        <button type="button" 
                class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500"
                data-action="click->dropdown#toggle"
                data-dropdown-target="button">
          <span class="sr-only">Open user menu</span>
          <div class="h-8 w-8 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full flex items-center justify-center">
            <span class="text-sm font-medium text-white">
              <%= @user.full_name.split.map(&:first).join.upcase %>
            </span>
          </div>
          <div class="hidden sm:block ml-3">
            <div class="text-left">
              <p class="text-sm font-medium text-gray-900"><%= @user.full_name %></p>
              <p class="text-xs text-gray-500"><%= @user.role.humanize %></p>
            </div>
          </div>
          <svg class="hidden sm:block ml-2 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>

        <!-- User dropdown menu -->
        <div class="hidden absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
             data-dropdown-target="menu"
             role="menu"
             aria-orientation="vertical"
             aria-labelledby="user-menu-button">
          <div class="py-1">
            <!-- User Info Header -->
            <div class="px-4 py-3 border-b border-gray-100">
              <p class="text-sm font-medium text-gray-900"><%= @user.full_name %></p>
              <p class="text-xs text-gray-500"><%= @user.email %></p>
              <p class="text-xs text-gray-400 mt-1">
                <%= @user.role.humanize %> • <%= @account.name %>
              </p>
            </div>

            <!-- Profile Link -->
            <%= link_to edit_user_registration_path,
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100",
                role: "menuitem" do %>
              <div class="flex items-center">
                <svg class="mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                Your Profile
              </div>
            <% end %>

            <!-- Account Settings -->
            <% if can_manage_account? %>
              <%= link_to "#",
                  class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100",
                  role: "menuitem" do %>
                <div class="flex items-center">
                  <svg class="mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                  </svg>
                  Account Settings
                </div>
              <% end %>
            <% end %>

            <!-- Preferences -->
            <%= link_to "#",
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100",
                role: "menuitem" do %>
              <div class="flex items-center">
                <svg class="mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                Preferences
              </div>
            <% end %>

            <!-- API Tokens (for developers) -->
            <% if @user.role.in?(['owner', 'admin']) %>
              <%= link_to "#",
                  class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100",
                  role: "menuitem" do %>
                <div class="flex items-center">
                  <svg class="mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1921 9z"/>
                  </svg>
                  API Tokens
                  <span class="ml-auto text-xs text-gray-400">Coming Soon</span>
                </div>
              <% end %>
            <% end %>

            <!-- Help & Support -->
            <%= link_to "#",
                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100",
                role: "menuitem" do %>
              <div class="flex items-center">
                <svg class="mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Help & Support
              </div>
            <% end %>

            <div class="border-t border-gray-100 mt-1"></div>

            <!-- Sign Out -->
            <%= link_to destroy_user_session_path,
                method: :delete,
                class: "block px-4 py-2 text-sm text-red-700 hover:bg-red-50 focus:bg-red-50",
                role: "menuitem",
                data: { confirm: "Are you sure you want to sign out?" } do %>
              <div class="flex items-center">
                <svg class="mr-3 h-4 w-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                Sign out
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
