<div class="flex flex-col h-full bg-white border-r border-gray-200 lg:border-r-0">
  <!-- Mobile close button -->
  <div class="lg:hidden flex items-center justify-between h-16 px-4 border-b border-gray-200">
    <h2 class="text-lg font-semibold text-gray-900">Menu</h2>
    <button type="button"
            class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
            data-action="click->navigation#closeMobile">
      <span class="sr-only">Close menu</span>
      <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
      </svg>
    </button>
  </div>
  <!-- Logo -->
  <div class="hidden lg:flex items-center h-16 flex-shrink-0 px-4 bg-gradient-to-r from-indigo-600 to-blue-600">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div class="h-8 w-8 bg-white rounded-lg flex items-center justify-center">
          <svg class="h-5 w-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
        </div>
      </div>
      <div class="ml-3">
        <h1 class="text-lg font-semibold text-white">DataReflow</h1>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto"
       role="navigation"
       aria-label="Main navigation">
    <%= link_to subdomain_root_path,
        class: nav_link_classes(request.path == subdomain_root_path),
        'aria-current': (request.path == subdomain_root_path ? 'page' : nil),
        'aria-label': 'Dashboard - Main overview page' do %>
      <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"/>
      </svg>
      Dashboard
    <% end %>

    <%= link_to pipelines_path,
        class: nav_link_classes(request.path.start_with?('/pipelines')),
        'aria-current': (request.path.start_with?('/pipelines') ? 'page' : nil),
        'aria-label': 'Pipelines - Manage data pipelines' do %>
      <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
      </svg>
      Pipelines
      <span class="ml-auto bg-indigo-100 text-indigo-600 text-xs font-medium px-2 py-1 rounded-full">
        <%= @pipeline_metrics&.dig(:total_pipelines) || current_account&.pipelines&.count || 0 %>
      </span>
    <% end %>

    <%= link_to projects_path,
        class: nav_link_classes(request.path.start_with?('/projects')),
        'aria-current': (request.path.start_with?('/projects') ? 'page' : nil),
        'aria-label': 'Projects - Manage your data projects' do %>
      <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
      </svg>
      Projects
      <span class="ml-auto bg-indigo-100 text-indigo-600 text-xs font-medium px-2 py-1 rounded-full">
        <%= @project_metrics&.dig(:total_projects) || current_account&.projects&.count || 0 %>
      </span>
    <% end %>

    <%= link_to analytics_path,
        class: nav_link_classes(request.path.start_with?('/analytics')),
        'aria-current': (request.path.start_with?('/analytics') ? 'page' : nil),
        'aria-label': 'Analytics - View data insights and reports' do %>
      <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
      </svg>
      Analytics
      <% if feature_available?(:advanced_analytics) %>
        <span class="ml-auto bg-purple-100 text-purple-600 text-xs font-medium px-2 py-1 rounded-full">
          Pro
        </span>
      <% end %>
    <% end %>

    <%= link_to team_members_path, 
        class: nav_link_classes(request.path.start_with?('/team_members')) do %>
      <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
      </svg>
      Team
      <span class="ml-auto bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">
        <%= current_account&.users&.count || 0 %>
      </span>
    <% end %>

    <%= link_to subscription_path, 
        class: nav_link_classes(request.path == subscription_path) do %>
      <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
      </svg>
      Billing
    <% end %>
  </nav>

  <!-- Account info -->
  <div class="flex-shrink-0 border-t border-gray-200 p-4">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div class="h-8 w-8 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full flex items-center justify-center">
          <span class="text-sm font-medium text-white">
            <%= @account.name&.first&.upcase || 'A' %>
          </span>
        </div>
      </div>
      <div class="ml-3 min-w-0 flex-1">
        <p class="text-sm font-medium text-gray-900 truncate">
          <%= @account.name %>
        </p>
        <p class="text-xs text-gray-500 truncate">
          <%= @account.subscription&.plan&.humanize || 'Free Plan' %>
        </p>
      </div>
    </div>
  </div>
</div>

<% content_for :helpers do %>
  <% 
    def nav_link_classes(active = false)
      base_classes = "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150"
      if active
        "#{base_classes} bg-indigo-50 border-r-2 border-indigo-500 text-indigo-700"
      else
        "#{base_classes} text-gray-700 hover:bg-gray-50 hover:text-gray-900"
      end
    end
  %>
<% end %>
